{"rustc": 15111467291198882907, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 9656904095642909417, "path": 11482895926138515453, "deps": [[5820056977320921005, "anstream", false, 1586535853710293376], [9394696648929125047, "anstyle", false, 5652308195112767079], [11166530783118767604, "strsim", false, 16671753580752529440], [11649982696571033535, "clap_lex", false, 16169936980472851214]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_builder-da7810d367137b59/dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}