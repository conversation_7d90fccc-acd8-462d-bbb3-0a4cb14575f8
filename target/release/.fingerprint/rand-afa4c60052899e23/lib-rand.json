{"rustc": 15111467291198882907, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 3179986570967055202, "deps": [[1573238666360410412, "rand_chacha", false, 17382975874703466912], [5330658427305787935, "libc", false, 9372423091174070989], [18130209639506977569, "rand_core", false, 16687047638858790653]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-afa4c60052899e23/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}