{"rustc": 15111467291198882907, "features": "[\"default\"]", "declared_features": "[\"debug\", \"default\", \"deprecated\", \"raw-deprecated\", \"unstable-markdown\", \"unstable-v5\"]", "target": 905583280159225126, "profile": 12613628788268674035, "path": 7036246487677310376, "deps": [[3060637413840920116, "proc_macro2", false, 7422874272814993066], [10640660562325816595, "syn", false, 17029264163343387634], [13077543566650298139, "heck", false, 752542371323651626], [17990358020177143287, "quote", false, 1240873261842976076]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap_derive-7008def1d14f0183/dep-lib-clap_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}