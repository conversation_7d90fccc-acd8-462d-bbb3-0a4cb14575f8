{"rustc": 15111467291198882907, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2040997289075261528, "path": 8212096197971754594, "deps": [[5230392855116717286, "equivalent", false, 6061808371939355711], [9150530836556604396, "allocator_api2", false, 8898769806154727339], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 5056527836303623955]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-f999a7c4321c012e/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}